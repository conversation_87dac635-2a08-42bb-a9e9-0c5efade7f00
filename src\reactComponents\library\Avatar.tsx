import React from 'react';
import MuiAvatar from '@mui/material/Avatar';
import { SxProps, Theme } from '@mui/material/styles';

type AvatarSize = 'sm' | 'md' | 'lg';

interface SizeConfig {
  height: number;
  width: number;
  fontSize: string;
  fontWeight: number;
}

const sizes: Record<AvatarSize, SizeConfig> = {
  sm: { height: 30, width: 30, fontSize: '12px', fontWeight: 600 },
  md: { height: 40, width: 40, fontSize: '16px', fontWeight: 600 },
  lg: { height: 60, width: 60, fontSize: '25px', fontWeight: 600 },
};

export interface AvatarProps {
  /**
   * The name to generate initials from. Should contain at least first and last name.
   */
  name?: string;
  /**
   * Additional styles to apply to the avatar using MUI's sx prop.
   */
  sx?: SxProps<Theme>;
  /**
   * The size variant of the avatar.
   * @default 'lg'
   */
  size?: AvatarSize;
}

const Avatar: React.FC<AvatarProps> = ({ name = '', sx = {}, size = 'lg' }) => {
  const generateInitials = (fullName: string): string => {
    const nameParts = fullName.trim().split(' ');

    if (nameParts.length === 0 || !nameParts[0]) {
      return '';
    }

    if (nameParts.length === 1) {
      return nameParts[0][0]?.toUpperCase() || '';
    }

    const firstInitial = nameParts[0][0] || '';
    const lastInitial = nameParts[nameParts.length - 1][0] || '';

    return `${firstInitial}${lastInitial}`.toUpperCase();
  };

  const initials = generateInitials(name);

  const combinedSx = [
    () => ({
      bgcolor: 'gray.200',
      border: '1px solid',
      borderColor: 'gray.300',
      color: 'text.primary',
      ...sizes[size],
      ...sx,
    }),
    (theme: Theme) =>
      theme.applyStyles('dark', {
        bgcolor: 'gray.700',
        color: 'white',
      }),
  ] as SxProps<Theme>;

  return <MuiAvatar sx={combinedSx}>{initials}</MuiAvatar>;
};

export default Avatar;