{"name": "fps", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "test:unit": "vitest --environment jsdom --root src/", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lintReact": "eslint . --ext .jsx,.tsx,.js,.ts --ignore-path .gitignore", "prePushLint": "sh -c 'git diff --name-only origin/$(git rev-parse --abbrev-ref HEAD) | xargs eslint --ext .jsx,.tsx,.js,.ts --ignore-path .gitignore -c ./.eslintrc.prepush.cjs'", "prepare": "husky", "format": "prettier --write ./src", "cypress:open": "cypress open", "test:e2e": "npm run test:e2e:chrome || npm run test:e2e:edge", "test:e2e:chrome": "cypress run --browser chrome --config-file cypress.config.mjs", "test:e2e:edge": "cypress run --browser edge --config-file cypress.config.mjs", "test:component": "cypress run --component"}, "dependencies": {"@castandcrew/common-ui-design-tokens": "^0.1.6", "@castandcrew/document-signer-react": "^2.0.7", "@castandcrew/dynamic-state-forms": "^0.0.1", "@castandcrew/platform-components": "^0.4.2", "@castandcrew/platform-ui": "^0.1.33", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/vue": "^1.7.7", "@heroicons/react": "^2.2.0", "@heroicons/vue": "^2.2.0", "@hotjar/browser": "^1.0.9", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.2", "@mui/system": "^6.4.2", "@mui/x-data-grid-pro": "^7.25.0", "@mui/x-date-pickers": "^7.25.0", "@novu/headless": "^0.24.2", "@novu/notification-center-vue": "^0.17.2", "@okta/okta-auth-js": "^7.11.1", "@okta/okta-react": "^6.9.0", "@pdfme/common": "^2.2.0", "@pdfme/ui": "^2.2.0", "@tailwindcss/forms": "^0.5.3", "@types/react": "^19.1.2", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.4", "country-flag-icons": "^1.5.18", "libphonenumber": "^0.0.10", "luxon": "^3.2.1", "mobx": "^6.13.5", "mobx-react-lite": "^4.1.0", "mobx-vue-lite": "^0.4.3", "notistack": "^3.0.2", "novu": "^0.24.1", "pdfjs": "^2.4.7", "pdfjs-dist": "3.11.174", "pinia": "^2.0.28", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "react-window": "^1.8.11", "signature_pad": "^4.1.5", "veaury": "^2.6.1", "vite-svg-loader": "^5.1.0", "vue": "^3.2.45", "vue-router": "4.3.0", "vue-the-mask": "^0.11.1"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@rushstack/eslint-patch": "^1.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/debug": "^4.1.12", "@types/jsdom": "^20.0.1", "@types/lodash": "^4.17.0", "@types/luxon": "^3.2.0", "@types/node": "^20.17.47", "@types/vue-the-mask": "^0.11.1", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.2.6", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.13", "cypress": "^14.2.1", "cypress-file-upload": "^5.0.8", "cypress-mochawesome-reporter": "^3.8.2", "eslint": "^8.22.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-vue": "^9.3.0", "husky": "^9.1.7", "jsdom": "^20.0.3", "jsonc-eslint-parser": "^2.4.0", "lint-staged": "^15.2.11", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "prettier": "^2.7.1", "tailwindcss": "^3.2.4", "typescript": "~4.7.4", "vite": "^5.2.7", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.0.4", "vue-tsc": "^1.8.11"}}