import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { describe, it, expect } from 'vitest';
import Avatar from '../Avatar';
import type { AvatarProps } from '../Avatar';

// Create a test theme for MUI components
const testTheme = createTheme();

// Helper function to render Avatar with theme provider
const renderAvatar = (props: AvatarProps = {}) => {
  return render(
    <ThemeProvider theme={testTheme}>
      <Avatar {...props} />
    </ThemeProvider>
  );
};

describe('Avatar Component', () => {
  describe('Rendering', () => {
    it('renders without crashing', () => {
      renderAvatar();
      expect(screen.getByRole('img')).toBeInTheDocument();
    });

    it('renders with default props', () => {
      renderAvatar();
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveTextContent('');
    });

    it('renders with custom name', () => {
      renderAvatar({ name: '<PERSON>e' });
      const avatar = screen.getByRole('img');
      expect(avatar).toHaveTextContent('JD');
    });
  });

  describe('Initials Generation', () => {
    it('generates correct initials for full name', () => {
      renderAvatar({ name: 'John Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('generates correct initials for single name', () => {
      renderAvatar({ name: 'John' });
      expect(screen.getByText('J')).toBeInTheDocument();
    });

    it('generates correct initials for multiple names', () => {
      renderAvatar({ name: 'John Michael Doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('handles names with extra spaces', () => {
      renderAvatar({ name: '  John   Doe  ' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('handles empty name', () => {
      renderAvatar({ name: '' });
      const avatar = screen.getByRole('img');
      expect(avatar).toHaveTextContent('');
    });

    it('handles undefined name', () => {
      renderAvatar({ name: undefined });
      const avatar = screen.getByRole('img');
      expect(avatar).toHaveTextContent('');
    });

    it('handles name with only spaces', () => {
      renderAvatar({ name: '   ' });
      const avatar = screen.getByRole('img');
      expect(avatar).toHaveTextContent('');
    });

    it('converts initials to uppercase', () => {
      renderAvatar({ name: 'john doe' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });
  });

  describe('Size Variants', () => {
    it('applies small size styles', () => {
      renderAvatar({ name: 'John Doe', size: 'sm' });
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
      // Note: We can't easily test computed styles in jsdom, but we can test that it renders
    });

    it('applies medium size styles', () => {
      renderAvatar({ name: 'John Doe', size: 'md' });
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
    });

    it('applies large size styles (default)', () => {
      renderAvatar({ name: 'John Doe', size: 'lg' });
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
    });

    it('uses large size as default when size is not specified', () => {
      renderAvatar({ name: 'John Doe' });
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Custom Styling', () => {
    it('accepts custom sx prop', () => {
      const customSx = { backgroundColor: 'red' };
      renderAvatar({ name: 'John Doe', sx: customSx });
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
    });

    it('renders without sx prop', () => {
      renderAvatar({ name: 'John Doe' });
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles special characters in names', () => {
      renderAvatar({ name: 'José María' });
      expect(screen.getByText('JM')).toBeInTheDocument();
    });

    it('handles numbers in names', () => {
      renderAvatar({ name: 'John2 Doe3' });
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('handles single character names', () => {
      renderAvatar({ name: 'A B' });
      expect(screen.getByText('AB')).toBeInTheDocument();
    });

    it('handles very long names', () => {
      renderAvatar({ name: 'Verylongfirstname Verylonglastname' });
      expect(screen.getByText('VV')).toBeInTheDocument();
    });
  });

  describe('TypeScript Props', () => {
    it('accepts all valid size values', () => {
      // These should compile without TypeScript errors
      renderAvatar({ size: 'sm' });
      renderAvatar({ size: 'md' });
      renderAvatar({ size: 'lg' });
    });

    it('accepts optional props', () => {
      // These should compile without TypeScript errors
      renderAvatar({});
      renderAvatar({ name: 'John' });
      renderAvatar({ size: 'md' });
      renderAvatar({ sx: {} });
    });
  });
});